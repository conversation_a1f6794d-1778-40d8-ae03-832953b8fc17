"use client";

import { useState } from "react";
import Link from "next/link";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Calendar, Clock, ArrowRight, BookOpen } from "lucide-react";
import ArticleContent from "@/components/article-content";
import TableOfContents from "@/components/table-of-contents";
import type { Post } from "@/lib/blog";

interface BlogArticleContentProps {
  post: Post;
  relatedPosts: Post[];
}

function formatDate(dateString: string) {
  const date = new Date(dateString);
  return date.toLocaleDateString('it-IT', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

function calculateReadingTime(content: string) {
  const wordsPerMinute = 200;
  const words = content.replace(/<[^>]*>/g, '').split(/\s+/).length;
  const minutes = Math.ceil(words / wordsPerMinute);
  return `${minutes} min`;
}

export default function BlogArticleContent({ post, relatedPosts }: BlogArticleContentProps) {
  const [headings, setHeadings] = useState<Array<{ id: string; text: string; level: number }>>([]);

  const handleHeadingsExtracted = (extractedHeadings: Array<{ id: string; text: string; level: number }>) => {
    setHeadings(extractedHeadings);
  };

  return (
    <>
      {/* Article Content */}
      <section className="py-16">
        <div className="container-custom">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-12">
              {/* Main Content */}
              <div className="lg:col-span-3">
                <article className="max-w-4xl">
                  <ArticleContent 
                    content={post.content} 
                    onHeadingsExtracted={handleHeadingsExtracted}
                  />
                </article>
              </div>

              {/* Sidebar with Table of Contents */}
              <div className="lg:col-span-1">
                <div className="sidebar-container">
                  <div className="sidebar-content space-y-6">
                    <TableOfContents headings={headings} />

                    {/* Article Stats */}
                    <Card className="sticky top-[28rem] z-sidebar-secondary border-0 bg-card/80 backdrop-blur-md shadow-large">
                      <CardHeader className="pb-4">
                        <h3 className="font-semibold text-foreground">Statistiche Articolo</h3>
                      </CardHeader>
                      <CardContent className="pt-0 space-y-4">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Tempo di lettura</span>
                          <Badge variant="secondary">{calculateReadingTime(post.content)}</Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Sezioni</span>
                          <Badge variant="outline">{headings.length}</Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Pubblicato</span>
                          <span className="text-sm font-medium">{formatDate(post.created_at)}</span>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Quick Navigation */}
                    <Card className="sticky top-[36rem] z-sidebar-secondary border-0 bg-gradient-to-br from-primary/5 to-secondary/5 backdrop-blur-md shadow-medium">
                      <CardContent className="p-6">
                        <div className="text-center space-y-4">
                          <div className="w-12 h-12 rounded-full bg-gradient-to-br from-primary to-primary-dark flex items-center justify-center mx-auto">
                            <BookOpen className="w-6 h-6 text-white" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-foreground mb-2">Hai domande?</h3>
                            <p className="text-sm text-muted-foreground mb-4">
                              Consulta il nostro test AI per una valutazione personalizzata
                            </p>
                            <Link
                              href="/test"
                              className="inline-flex items-center gap-2 text-primary hover:text-primary-dark font-medium text-sm transition-colors"
                            >
                              Inizia il test
                              <ArrowRight className="w-4 h-4" />
                            </Link>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Enhanced Related Articles */}
      {relatedPosts.length > 0 && (
        <section className="py-20 bg-gradient-to-br from-muted/30 via-background to-primary/5">
          <div className="container-custom">
            <div className="max-w-6xl mx-auto">
              <div className="text-center space-y-4 mb-16">
                <Badge variant="outline" className="mb-4">
                  <BookOpen className="w-4 h-4 mr-2" />
                  Letture Consigliate
                </Badge>
                <h2 className="text-3xl sm:text-4xl font-bold text-foreground">
                  Articoli Correlati
                </h2>
                <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                  Approfondisci la tua conoscenza con questi articoli selezionati
                </p>
              </div>
              
              <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                {relatedPosts.map((relatedPost) => (
                  <Link key={relatedPost.id} href={`/blog/${relatedPost.slug}`}>
                    <Card className="group h-full hover:shadow-large transition-all duration-300 hover:-translate-y-2 border-0 bg-card/50 backdrop-blur-sm">
                      <CardHeader className="pb-4">
                        <div className="flex items-center justify-between mb-4">
                          <Badge variant="secondary" className="text-xs">
                            Articolo
                          </Badge>
                          <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-primary to-primary-dark flex items-center justify-center shadow-medium">
                            <BookOpen className="w-5 h-5 text-white" />
                          </div>
                        </div>
                        
                        <h3 className="text-xl font-bold text-foreground group-hover:text-primary transition-colors line-clamp-2 mb-3">
                          {relatedPost.title}
                        </h3>
                        <p className="text-muted-foreground line-clamp-3 leading-relaxed">
                          {relatedPost.excerpt}
                        </p>
                      </CardHeader>
                      
                      <CardContent className="pt-0">
                        <div className="flex items-center justify-between text-xs text-muted-foreground mb-4">
                          <div className="flex items-center gap-1">
                            <Calendar className="w-3 h-3" />
                            {formatDate(relatedPost.created_at)}
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            {calculateReadingTime(relatedPost.content)}
                          </div>
                        </div>
                        
                        <div className="pt-4 border-t border-border/50">
                          <div className="flex items-center text-primary text-sm font-medium group-hover:text-primary-dark transition-colors">
                            Leggi articolo
                            <ArrowRight className="w-3 h-3 ml-1 group-hover:translate-x-1 transition-transform" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </section>
      )}
    </>
  );
}

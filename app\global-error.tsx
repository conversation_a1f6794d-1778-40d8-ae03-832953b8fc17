"use client";

import { useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { RefreshCw, Home, AlertTriangle } from "lucide-react";

interface GlobalErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function GlobalError({ error, reset }: GlobalErrorProps) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error("Global application error:", error);
  }, [error]);

  return (
    <html lang="it">
      <body className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50 flex items-center justify-center p-4">
        <div className="max-w-md mx-auto text-center space-y-6">
          {/* Error Icon */}
          <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
            <AlertTriangle className="w-8 h-8 text-red-600" />
          </div>

          {/* Error Message */}
          <div className="space-y-3">
            <h1 className="text-2xl font-bold text-gray-900">
              Errore Critico del Sistema
            </h1>
            <p className="text-gray-600">
              Si è verificato un errore grave nell'applicazione. 
              Il nostro team tecnico è stato notificato automaticamente.
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col gap-3">
            <Button 
              onClick={reset}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Ricarica Applicazione
            </Button>
            <Button 
              variant="outline"
              onClick={() => window.location.href = "/"}
              className="border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              <Home className="w-4 h-4 mr-2" />
              Vai alla Home Page
            </Button>
          </div>

          {/* Contact Information */}
          <Card className="bg-white/80 border-gray-200">
            <CardHeader>
              <CardTitle className="text-sm text-gray-700">
                Hai bisogno di aiuto?
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-xs text-gray-600">
                Se il problema persiste, contatta il nostro supporto tecnico.
                Includi il codice errore se disponibile.
              </p>
              {error.digest && (
                <p className="text-xs text-gray-500 mt-2 font-mono">
                  Codice: {error.digest}
                </p>
              )}
            </CardContent>
          </Card>

          {/* Technical Details (Development Only) */}
          {process.env.NODE_ENV === "development" && (
            <Card className="bg-gray-100 border-gray-300 text-left">
              <CardHeader>
                <CardTitle className="text-sm text-gray-700">
                  Dettagli Tecnici (Solo Sviluppo)
                </CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="text-xs text-gray-600 overflow-auto whitespace-pre-wrap">
                  {error.message}
                  {error.stack && `\n\nStack Trace:\n${error.stack}`}
                </pre>
              </CardContent>
            </Card>
          )}
        </div>
      </body>
    </html>
  );
}

import { getPostBySlug, getAllPosts } from "@/lib/blog";
import { notFound } from "next/navigation";
import type { Metadata } from "next";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  Calendar,
  Clock,
  ArrowLeft,
  BookOpen,
  ChevronRight,
  Home,
  User,
  Eye
} from "lucide-react";
import Navbar from "@/app/components/Navbar";
import Footer from "@/app/components/Footer";
import ReadingProgress from "@/components/reading-progress";
import SocialShare from "@/components/social-share";
import BlogArticleContent from "@/components/blog-article-content";
import ToastProvider from "@/components/toast-provider";

type Props = {
  params: Promise<{ slug: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

function formatDate(dateString: string) {
  const date = new Date(dateString);
  return date.toLocaleDateString('it-IT', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

function calculateReadingTime(content: string) {
  const wordsPerMinute = 200;
  const words = content.replace(/<[^>]*>/g, '').split(/\s+/).length;
  const minutes = Math.ceil(words / wordsPerMinute);
  return `${minutes} min`;
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const resolvedParams = await params;
  const post = await getPostBySlug(resolvedParams.slug);
  if (!post) return {};
  return {
    title: `${post.title} | Scabbia Rimedi`,
    description: post.excerpt,
    keywords: "scabbia, sintomi, trattamento, prevenzione, guida medica",
    openGraph: {
      title: post.title,
      description: post.excerpt,
      type: 'article',
      publishedTime: post.created_at,
    },
  };
}

export default async function BlogPostPage({ params }: Props) {
  const resolvedParams = await params;
  const post = await getPostBySlug(resolvedParams.slug);
  if (!post) return notFound();

  // Get related posts (excluding current post)
  const allPosts = await getAllPosts();
  const relatedPosts = allPosts.filter(p => p.id !== post.id).slice(0, 3);

  return (
    <>
      <ReadingProgress />
      <Navbar />
      <main className="min-h-screen bg-background">
        {/* Breadcrumb Navigation */}
        <section className="py-6 bg-muted/30">
          <div className="container-custom">
            <nav className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Link href="/" className="hover:text-foreground transition-colors">
                <Home className="w-4 h-4" />
              </Link>
              <ChevronRight className="w-4 h-4" />
              <Link href="/blog" className="hover:text-foreground transition-colors">
                Blog
              </Link>
              <ChevronRight className="w-4 h-4" />
              <span className="text-foreground font-medium truncate">
                {post.title}
              </span>
            </nav>
          </div>
        </section>

        {/* Enhanced Article Header */}
        <section className="py-16 bg-gradient-to-br from-primary-light via-background to-secondary/10">
          <div className="container-custom">
            <div className="max-w-4xl mx-auto">
              {/* Back Button */}
              <Link href="/blog" className="inline-flex items-center text-primary hover:text-primary-dark transition-colors mb-8 group">
                <ArrowLeft className="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform" />
                Torna al Blog
              </Link>

              {/* Article Meta */}
              <div className="space-y-8">
                <div className="flex items-center gap-4">
                  <Badge variant="outline" className="bg-white/80 backdrop-blur-sm">
                    <BookOpen className="w-4 h-4 mr-2" />
                    Articolo Medico
                  </Badge>
                  <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20">
                    <Eye className="w-4 h-4 mr-2" />
                    {calculateReadingTime(post.content)} di lettura
                  </Badge>
                </div>

                <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground leading-tight">
                  {post.title}
                </h1>

                {post.excerpt && (
                  <p className="text-xl sm:text-2xl text-muted-foreground leading-relaxed max-w-3xl">
                    {post.excerpt}
                  </p>
                )}

                {/* Enhanced Meta Information */}
                <Card className="bg-white/50 backdrop-blur-sm border-0 shadow-medium">
                  <CardContent className="p-6">
                    <div className="flex flex-wrap items-center justify-between gap-6">
                      <div className="flex items-center gap-6">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 rounded-full bg-gradient-to-br from-primary to-primary-dark flex items-center justify-center">
                            <User className="w-5 h-5 text-white" />
                          </div>
                          <div>
                            <p className="font-semibold text-foreground">Dr. Medico</p>
                            <p className="text-sm text-muted-foreground">Specialista Dermatologo</p>
                          </div>
                        </div>

                        <div className="h-8 w-px bg-border"></div>

                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <div className="flex items-center gap-2">
                            <Calendar className="w-4 h-4" />
                            <span>{formatDate(post.created_at)}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Clock className="w-4 h-4" />
                            <span>{calculateReadingTime(post.content)} di lettura</span>
                          </div>
                        </div>
                      </div>

                      <SocialShare
                        title={post.title}
                        url={`/blog/${post.slug}`}
                        description={post.excerpt}
                      />
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </section>

        <Separator />

        {/* Article Content with Table of Contents */}
        <BlogArticleContent post={post} relatedPosts={relatedPosts} />
      </main>
      <Footer />
      <ToastProvider />
    </>
  );
}

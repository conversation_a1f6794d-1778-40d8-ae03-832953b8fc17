@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Modern Medical Color Palette */
    --background: 0 0% 100%;
    --foreground: 210 40% 8%;
    --card: 0 0% 100%;
    --card-foreground: 210 40% 8%;
    --popover: 0 0% 100%;
    --popover-foreground: 210 40% 8%;

    /* Primary - Medical Blue */
    --primary: 210 100% 50%;
    --primary-foreground: 0 0% 98%;
    --primary-light: 210 100% 95%;
    --primary-dark: 210 100% 40%;

    /* Secondary - Teal */
    --secondary: 180 25% 95%;
    --secondary-foreground: 180 25% 15%;
    --secondary-accent: 180 84% 60%;

    /* Success - Green */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;
    --success-light: 142 76% 95%;

    /* Warning - Amber */
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 98%;
    --warning-light: 38 92% 95%;

    /* Error - Red */
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --destructive-light: 0 84% 95%;

    /* Neutral Colors */
    --muted: 210 40% 98%;
    --muted-foreground: 210 40% 45%;
    --accent: 210 40% 96%;
    --accent-foreground: 210 40% 10%;

    /* Border & Input */
    --border: 210 40% 90%;
    --input: 210 40% 90%;
    --ring: 210 100% 50%;

    /* Chart Colors */
    --chart-1: 210 100% 50%;
    --chart-2: 180 84% 60%;
    --chart-3: 142 76% 36%;
    --chart-4: 38 92% 50%;
    --chart-5: 0 84% 60%;

    /* Border Radius */
    --radius: 0.75rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  }

  .dark {
    --background: 210 40% 3%;
    --foreground: 210 40% 98%;
    --card: 210 40% 4%;
    --card-foreground: 210 40% 98%;
    --popover: 210 40% 4%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 100% 60%;
    --primary-foreground: 210 40% 3%;
    --primary-light: 210 100% 10%;
    --primary-dark: 210 100% 70%;

    --secondary: 210 40% 10%;
    --secondary-foreground: 210 40% 98%;
    --secondary-accent: 180 84% 50%;

    --success: 142 76% 46%;
    --success-foreground: 210 40% 3%;
    --success-light: 142 76% 10%;

    --warning: 38 92% 60%;
    --warning-foreground: 210 40% 3%;
    --warning-light: 38 92% 10%;

    --destructive: 0 84% 70%;
    --destructive-foreground: 210 40% 3%;
    --destructive-light: 0 84% 10%;

    --muted: 210 40% 10%;
    --muted-foreground: 210 40% 65%;
    --accent: 210 40% 10%;
    --accent-foreground: 210 40% 98%;

    --border: 210 40% 18%;
    --input: 210 40% 18%;
    --ring: 210 100% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Focus styles */
  *:focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }

  /* Selection styles */
  ::selection {
    @apply bg-primary/20 text-primary-foreground;
  }
}

@layer components {
  /* Container utilities */
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Text utilities */
  .text-gradient {
    @apply bg-gradient-to-r from-primary to-secondary-accent bg-clip-text text-transparent;
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.5s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.3s ease-out;
  }

  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-bounce {
    animation: bounce 1s infinite;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-shimmer {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
  }

  .animate-progress {
    animation: progressFill 0.8s ease-out;
  }
}

@layer utilities {
  /* Container utilities */
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Custom shadows */
  .shadow-soft {
    box-shadow: var(--shadow);
  }

  .shadow-medium {
    box-shadow: var(--shadow-md);
  }

  .shadow-large {
    box-shadow: var(--shadow-lg);
  }

  .shadow-extra-large {
    box-shadow: var(--shadow-xl);
  }

  /* Glass effect */
  .glass {
    @apply bg-white/80 backdrop-blur-sm border border-white/20;
  }

  .glass-dark {
    @apply bg-black/80 backdrop-blur-sm border border-white/10;
  }

  /* Text utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* Blog Article Styles */
@layer components {
  .prose {
    @apply text-foreground max-w-none;
  }

  .enhanced-prose h1 {
    @apply text-3xl font-bold text-foreground mt-8 mb-6 leading-tight;
    position: relative;
  }

  .enhanced-prose h1::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, hsl(var(--primary)), hsl(var(--secondary-accent)));
    border-radius: 2px;
  }

  .enhanced-prose h2 {
    @apply text-2xl font-semibold text-foreground mt-12 mb-6 leading-tight;
    position: relative;
    padding-left: 20px;
  }

  .enhanced-prose h2::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 24px;
    background: linear-gradient(180deg, hsl(var(--primary)), hsl(var(--secondary-accent)));
    border-radius: 2px;
  }

  .enhanced-prose h3 {
    @apply text-xl font-semibold text-foreground mt-8 mb-4 leading-tight;
  }

  .enhanced-prose h4 {
    @apply text-lg font-semibold text-foreground mt-6 mb-3 leading-tight;
  }

  .enhanced-prose p {
    @apply text-muted-foreground leading-relaxed mb-6 text-base;
  }

  .enhanced-prose a {
    @apply text-primary hover:text-primary-dark underline underline-offset-2 transition-colors font-medium;
  }

  .enhanced-prose strong {
    @apply text-foreground font-semibold;
  }

  .enhanced-prose em {
    @apply text-muted-foreground italic;
  }

  .enhanced-list {
    @apply text-muted-foreground mb-6 pl-0 space-y-3;
  }

  .enhanced-list li {
    @apply relative pl-8 leading-relaxed;
  }

  .enhanced-list li::before {
    content: '';
    position: absolute;
    left: 0;
    top: 10px;
    width: 6px;
    height: 6px;
    background: hsl(var(--primary));
    border-radius: 50%;
  }

  .enhanced-list.numbered {
    counter-reset: list-counter;
  }

  .enhanced-list.numbered li {
    counter-increment: list-counter;
  }

  .enhanced-list.numbered li::before {
    content: counter(list-counter);
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--secondary-accent)));
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    top: 2px;
  }

  .enhanced-quote {
    @apply border-l-4 border-primary pl-6 pr-4 italic text-muted-foreground bg-gradient-to-r from-muted/50 to-transparent py-4 my-8 rounded-r-lg relative;
  }

  .enhanced-quote::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: 20px;
    font-size: 3rem;
    color: hsl(var(--primary));
    font-family: serif;
    line-height: 1;
  }

  .enhanced-divider {
    @apply my-12 border-0 h-px bg-gradient-to-r from-transparent via-border to-transparent;
  }

  .table-container {
    @apply overflow-x-auto my-8 rounded-lg border border-border shadow-medium;
  }

  .enhanced-table {
    @apply w-full border-collapse bg-card;
  }

  .enhanced-table th {
    @apply bg-gradient-to-r from-muted to-muted/80 font-semibold text-foreground px-6 py-4 text-left border-b border-border;
  }

  .enhanced-table td {
    @apply text-muted-foreground px-6 py-4 border-b border-border/50;
  }

  .enhanced-table tr:hover {
    @apply bg-muted/30;
  }

  .enhanced-prose code {
    @apply text-primary bg-muted px-2 py-1 rounded text-sm font-mono;
  }

  .enhanced-prose pre {
    @apply bg-muted p-6 rounded-lg overflow-x-auto text-sm font-mono mb-6 border border-border;
  }

  .enhanced-prose img {
    @apply rounded-lg shadow-large my-8 w-full;
  }

  /* Enhanced Script and Code Block Styles */
  .script-block-container,
  .code-block-container {
    @apply my-8 rounded-xl border border-border shadow-large bg-card;
    overflow: visible; /* Allow proper overflow handling */
  }

  .script-block-header,
  .code-block-header {
    @apply flex items-center justify-between px-6 py-3 bg-gradient-to-r from-muted to-muted/80 border-b border-border;
    flex-shrink: 0; /* Prevent header from shrinking */
  }

  .script-block-label,
  .code-block-label {
    @apply text-sm font-semibold text-foreground flex items-center gap-2;
  }

  .script-block-label::before {
    content: '⚡';
    @apply text-warning;
  }

  .code-block-label::before {
    content: '</>';
    @apply text-primary font-mono;
  }

  .script-block-type,
  .code-block-language {
    @apply text-xs font-medium text-muted-foreground bg-background px-2 py-1 rounded border;
  }

  /* Wrapper for proper scrolling */
  .script-block-wrapper,
  .code-block-wrapper {
    @apply overflow-hidden;
    border-radius: 0 0 0.75rem 0.75rem; /* Match container border radius */
  }

  .script-block,
  .code-block {
    @apply bg-gradient-to-br from-muted/30 to-background p-6 text-sm font-mono leading-relaxed;
    margin: 0 !important;
    overflow-x: auto;
    overflow-y: visible;
    max-width: 100%;
    white-space: pre;
  }

  .script-block code,
  .code-block code {
    @apply text-foreground;
    background: none !important;
    padding: 0 !important;
    white-space: pre;
    word-wrap: normal;
    overflow-wrap: normal;
  }

  /* JSON-LD specific styling */
  .script-block-container .script-block {
    @apply bg-gradient-to-br from-primary/5 to-secondary/5;
  }

  /* Copy button for code blocks */
  .script-block-container:hover .copy-button,
  .code-block-container:hover .copy-button {
    @apply opacity-100;
  }

  .copy-button {
    @apply absolute top-3 right-3 opacity-0 transition-all duration-200 bg-background/90 backdrop-blur-sm border border-border rounded-md p-2 text-muted-foreground hover:text-foreground hover:bg-muted hover:shadow-medium cursor-pointer;
    z-index: 10;
  }

  .copy-button:hover {
    transform: scale(1.05);
  }

  .copy-button svg {
    @apply w-4 h-4;
  }

  /* Always show copy button on mobile */
  @media (max-width: 768px) {
    .copy-button {
      @apply opacity-100;
    }
  }

  /* FAQ Section Styles */
  .faq-section {
    @apply relative;
  }

  .faq-section::before {
    content: '';
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 2px;
    background: linear-gradient(90deg, transparent, hsl(var(--primary)), transparent);
  }

  /* Article Content Enhancements */
  .article-content {
    @apply relative;
  }

  .article-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: -20px;
    width: 2px;
    height: 100%;
    background: linear-gradient(180deg, hsl(var(--primary)), transparent, hsl(var(--secondary-accent)));
    opacity: 0.3;
  }

  /* Responsive adjustments */
  @media (max-width: 1024px) {
    .article-content::before {
      display: none;
    }
  }

  /* Smooth scroll offset for headings */
  .scroll-mt-20 {
    scroll-margin-top: 5rem;
  }

  /* Enhanced prose responsive typography */
  @media (max-width: 768px) {
    .enhanced-prose h1 {
      @apply text-2xl;
    }

    .enhanced-prose h2 {
      @apply text-xl;
    }

    .enhanced-prose h3 {
      @apply text-lg;
    }

    /* Mobile code block adjustments */
    .script-block-container,
    .code-block-container {
      @apply mx-0 rounded-lg;
    }

    .script-block-header,
    .code-block-header {
      @apply px-4 py-2 text-xs;
    }

    .script-block,
    .code-block {
      @apply p-4 text-xs;
    }
  }

  /* Sidebar and Layout Fixes */
  .sidebar-container {
    @apply relative;
  }

  .sidebar-content {
    @apply relative;
  }

  /* Z-index management */
  .z-navbar {
    z-index: 50;
  }

  .z-reading-progress {
    z-index: 51;
  }

  .z-sidebar-primary {
    z-index: 30;
  }

  .z-sidebar-secondary {
    z-index: 20;
  }

  .z-content {
    z-index: 10;
  }

  /* Prevent layout shifts during scroll */
  .sidebar-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100vh;
    pointer-events: none;
  }

  /* Mobile sidebar adjustments */
  @media (max-width: 1024px) {
    .sidebar-container {
      @apply static;
    }

    .sidebar-content > * {
      @apply static !important;
    }

    .sidebar-content {
      @apply flex flex-col gap-6;
    }
  }

  /* Smooth scroll behavior for headings */
  .scroll-mt-20 {
    scroll-margin-top: 6rem;
  }

  @media (max-width: 768px) {
    .scroll-mt-20 {
      scroll-margin-top: 4rem;
    }
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes progressFill {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0%);
  }
}

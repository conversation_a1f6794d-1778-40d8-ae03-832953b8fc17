"use client";

import { motion, AnimatePresence } from "framer-motion";
import { AlertCircle, CheckCircle, Info, AlertTriangle } from "lucide-react";
import { cn } from "@/lib/utils";
import { ValidationResult } from "@/lib/form-validation";

interface FormErrorProps {
  error?: string | string[] | null;
  validation?: ValidationResult;
  className?: string;
  variant?: 'error' | 'warning' | 'success' | 'info';
  showIcon?: boolean;
  animate?: boolean;
}

interface FormFieldProps extends FormErrorProps {
  label?: string;
  required?: boolean;
  children: React.ReactNode;
  description?: string;
}

const variantConfig = {
  error: {
    icon: AlertCircle,
    textColor: 'text-destructive',
    iconColor: 'text-destructive',
    bgColor: 'bg-destructive/5',
    borderColor: 'border-destructive/20'
  },
  warning: {
    icon: AlertTriangle,
    textColor: 'text-warning',
    iconColor: 'text-warning',
    bgColor: 'bg-warning/5',
    borderColor: 'border-warning/20'
  },
  success: {
    icon: CheckCircle,
    textColor: 'text-success',
    iconColor: 'text-success',
    bgColor: 'bg-success/5',
    borderColor: 'border-success/20'
  },
  info: {
    icon: Info,
    textColor: 'text-blue-600',
    iconColor: 'text-blue-600',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200'
  }
};

export function FormError({
  error,
  validation,
  className,
  variant = 'error',
  showIcon = true,
  animate = true
}: FormErrorProps) {
  // Determine what to display
  let messages: string[] = [];
  let displayVariant = variant;

  if (validation) {
    if (validation.errors.length > 0) {
      messages = validation.errors;
      displayVariant = 'error';
    } else if (validation.warnings && validation.warnings.length > 0) {
      messages = validation.warnings;
      displayVariant = 'warning';
    }
  } else if (error) {
    messages = Array.isArray(error) ? error : [error];
  }

  if (messages.length === 0) return null;

  const config = variantConfig[displayVariant];
  const IconComponent = config.icon;

  const content = (
    <div className={cn(
      "flex items-start gap-2 text-sm",
      config.textColor,
      className
    )}>
      {showIcon && (
        <IconComponent className={cn("w-4 h-4 flex-shrink-0 mt-0.5", config.iconColor)} />
      )}
      <div className="flex-1 min-w-0">
        {messages.length === 1 ? (
          <p>{messages[0]}</p>
        ) : (
          <ul className="space-y-1">
            {messages.map((message, index) => (
              <li key={index} className="flex items-start gap-1">
                <span className="w-1 h-1 bg-current rounded-full mt-2 flex-shrink-0" />
                <span>{message}</span>
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );

  if (!animate) {
    return content;
  }

  return (
    <AnimatePresence mode="wait">
      <motion.div
        initial={{ opacity: 0, height: 0, marginTop: 0 }}
        animate={{ opacity: 1, height: 'auto', marginTop: 4 }}
        exit={{ opacity: 0, height: 0, marginTop: 0 }}
        transition={{ duration: 0.2, ease: "easeInOut" }}
        className="overflow-hidden"
      >
        {content}
      </motion.div>
    </AnimatePresence>
  );
}

// Complete form field with label, input, and error display
export function FormField({
  label,
  required,
  children,
  description,
  error,
  validation,
  className,
  variant = 'error',
  showIcon = true,
  animate = true
}: FormFieldProps) {
  const hasError = (validation && !validation.isValid) || error;
  const hasSuccess = validation && validation.isValid && !error;

  return (
    <div className={cn("space-y-2", className)}>
      {/* Label */}
      {label && (
        <label className="text-sm font-medium text-foreground flex items-center gap-1">
          {label}
          {required && (
            <span className="text-destructive" aria-label="Campo obbligatorio">
              *
            </span>
          )}
        </label>
      )}

      {/* Description */}
      {description && (
        <p className="text-xs text-muted-foreground">
          {description}
        </p>
      )}

      {/* Input Field */}
      <div className="relative">
        {children}
        
        {/* Success indicator */}
        {hasSuccess && showIcon && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="absolute right-3 top-1/2 transform -translate-y-1/2"
          >
            <CheckCircle className="w-4 h-4 text-success" />
          </motion.div>
        )}
      </div>

      {/* Error/Warning Messages */}
      <FormError
        error={error}
        validation={validation}
        variant={variant}
        showIcon={showIcon}
        animate={animate}
      />
    </div>
  );
}

// Inline validation feedback for real-time validation
export function InlineValidation({
  validation,
  showSuccess = true,
  className
}: {
  validation?: ValidationResult;
  showSuccess?: boolean;
  className?: string;
}) {
  if (!validation) return null;

  const hasErrors = validation.errors.length > 0;
  const hasWarnings = validation.warnings && validation.warnings.length > 0;
  const isValid = validation.isValid;

  return (
    <div className={cn("space-y-1", className)}>
      <AnimatePresence>
        {/* Errors */}
        {hasErrors && (
          <motion.div
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -10 }}
            transition={{ duration: 0.2 }}
          >
            <FormError validation={validation} variant="error" animate={false} />
          </motion.div>
        )}

        {/* Warnings */}
        {!hasErrors && hasWarnings && (
          <motion.div
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -10 }}
            transition={{ duration: 0.2 }}
          >
            <FormError validation={validation} variant="warning" animate={false} />
          </motion.div>
        )}

        {/* Success */}
        {!hasErrors && !hasWarnings && isValid && showSuccess && (
          <motion.div
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -10 }}
            transition={{ duration: 0.2 }}
            className="flex items-center gap-2 text-sm text-success"
          >
            <CheckCircle className="w-4 h-4" />
            <span>Perfetto!</span>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

// Form summary showing all errors at once
export function FormErrorSummary({
  validationResults,
  title = "Correggi i seguenti errori:",
  className
}: {
  validationResults: Record<string, ValidationResult>;
  title?: string;
  className?: string;
}) {
  const allErrors = Object.entries(validationResults)
    .filter(([_, result]) => !result.isValid)
    .flatMap(([field, result]) => 
      result.errors.map(error => ({ field, error }))
    );

  if (allErrors.length === 0) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={cn(
        "p-4 rounded-lg border border-destructive/20 bg-destructive/5",
        className
      )}
    >
      <div className="flex items-start gap-3">
        <AlertCircle className="w-5 h-5 text-destructive flex-shrink-0 mt-0.5" />
        <div className="flex-1">
          <h4 className="font-medium text-destructive mb-2">{title}</h4>
          <ul className="space-y-1 text-sm text-destructive">
            {allErrors.map((item, index) => (
              <li key={index} className="flex items-start gap-2">
                <span className="w-1 h-1 bg-destructive rounded-full mt-2 flex-shrink-0" />
                <span>{item.error}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </motion.div>
  );
}
